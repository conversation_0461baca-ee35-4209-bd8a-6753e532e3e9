# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development
.env.production

# vercel
.vercel
.tmp

# typescript
*.tsbuildinfo
next-env.d.ts

Makefile
.wrangler
supabase
.*next
.dev.vars*
.source
