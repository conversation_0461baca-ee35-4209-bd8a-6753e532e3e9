# Brat Generator Component

A React component for generating custom Brat-style album covers with advanced customization options.

## Features

- **Text Input**: Users can input custom text that will be displayed on the cover
- **Advanced Color Controls**:
  - Custom background color with hex input and clickable color preview
  - Custom text color with hex input and clickable color preview
  - Interactive color picker with:
    - Clickable/draggable gradient area for saturation and lightness
    - Clickable/draggable hue bar for color selection
    - RGB inputs with real-time sync
    - Visual indicators showing current selection
  - Click outside to close color picker
- **Smart Auto-fill**: Automatically optimizes font size to beautifully fit your text in the background
- **Manual Font Size Control**: Adjustable font size from 20px to 120px with slider (when auto-fill is disabled)
- **Blur Effect**: Adjustable blur effect applied only to text (0px to 10px) - background remains sharp
- **Text Alignment**: Left, Center, or Right alignment with immediate visual feedback in preview
- **Live Preview**: Real-time preview with auto-fill optimization and cross-device consistency
- **Download Functionality**: Generate and download the cover as a PNG image (2000×2000px high-resolution square format)
- **Responsive Design**: Two-column layout on desktop, single column on mobile

## Usage

The component is already integrated into the Hero section of the landing page. It appears below the hero buttons.

```tsx
import BratGenerator from "@/components/brat-generator";

export default function MyPage() {
  return (
    <div>
      <BratGenerator />
    </div>
  );
}
```

## Layout

The component features a modern two-column layout:

### Left Column - Preview
- Real-time preview of the Brat cover
- Shows all applied effects (color, font size, blur, alignment)
- Square aspect ratio matching the final output

### Right Column - Controls
- **Advanced Color Controls**:
  - Hex input fields for precise color values
  - Clickable color preview squares with red borders (as shown in your image)
  - Interactive color picker popup with:
    - **Clickable/Draggable gradient area** - Click or drag to select saturation and lightness
    - **Clickable/Draggable hue bar** - Click or drag to select base color hue
    - **RGB input fields** (R, G, B values 0-255) with real-time sync
    - **Visual indicators** - White circles show current selection position
    - **Real-time preview** - All changes instantly reflected in preview
- **Font Size Slider**: Visual slider with current value display (20px - 120px)
- **Blur Effect Slider**: Visual slider with current value display (0px - 10px) - applies only to text, not background
- **Text Alignment Buttons**: Left, Center, Right alignment with accurate positioning for each text line
- **Download Button**: Generate and save the final image

## Default Settings

- **Format**: 1:1 Square (2000×2000px high-resolution)
- **Background Color**: Brat Green (#8ACE00) - The iconic Charli XCX Brat green
- **Text Color**: Black (#000000)
- **Auto-fill**: Enabled (font size automatically optimized)
- **Manual Font Size**: 60px (when auto-fill is disabled)
- **Blur Effect**: 0px (no blur)
- **Text Alignment**: Center
- **Default Text**: "brat"

## Technical Details

- Built with React hooks (useState, useRef, useEffect)
- Uses HTML5 Canvas for image generation with preview-matching algorithms
- **SSR Compatible** - Handles server-side rendering gracefully with client-side hydration
- Responsive design with Tailwind CSS
- Integrates with the project's UI component library
- Uses Lucide React icons for the download button
- **High-Resolution Square Format**: 2000×2000px ultra-high-quality square output
- **Smart Layout Technology**: Intelligent font size optimization for perfect text fitting
- **Preview-Download Consistency**: Canvas rendering precisely matches web preview through:
  - **Proportional scaling** - Scaling based on canvas smaller dimension vs 400px reference
  - **Fixed square preview** - CSS aspect-square with absolute positioning prevents content overflow
  - **Overflow protection** - Long text content doesn't break the fixed 1:1 ratio
  - **Smart sizing algorithm** - Binary search finds optimal font size to fit all your text beautifully
  - **High-resolution scaling** - 5x scaling factor from 400px preview to 2000px canvas
  - **Consistent blur effects** - Blur scaling maintains visual consistency
  - **Optimized padding** - 4% padding (80px) for closer text positioning
  - **High-precision text wrapping** - Uses high-DPI canvas measurement for accurate line breaking
  - **Cross-device consistency** - Conservative wrapping ensures identical display on all devices
  - **Device-aware measurement** - Accounts for devicePixelRatio for precise text measurement
  - **SSR safe** - Graceful fallback during server-side rendering, full functionality on client
  - **Consistent padding** - Both preview and canvas use 4% padding for closer text positioning

## Dependencies

- React
- Tailwind CSS
- Lucide React (for icons)
- Project's UI components (@/components/ui/button, @/components/ui/input)
