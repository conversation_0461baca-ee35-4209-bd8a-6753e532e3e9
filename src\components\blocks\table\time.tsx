"use client";

import moment from "moment";
import { useEffect, useState } from "react";

export default function TableItemTime({
  value,
  options,
  className,
}: {
  value: number;
  options?: any;
  className?: string;
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a static format during SSR to avoid hydration mismatch
    return (
      <div className={className}>
        {moment(value).format("YYYY-MM-DD HH:mm:ss")}
      </div>
    );
  }

  return (
    <div className={className}>
      {options?.format
        ? moment(value).format(options?.format)
        : moment(value).fromNow()}
    </div>
  );
}
