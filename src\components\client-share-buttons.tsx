"use client";

import { useEffect, useState } from "react";

export default function ClientShareButtons() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 在服务器端和客户端首次渲染时返回空的占位符
  if (!mounted) {
    return (
      <div className="h-10 w-full flex items-center justify-center">
        <div className="text-sm text-muted-foreground">Loading share buttons...</div>
      </div>
    );
  }

  // 只有在客户端挂载后才渲染ShareThis按钮
  return (
    <div className="sharethis-inline-share-buttons" />
  );
}
