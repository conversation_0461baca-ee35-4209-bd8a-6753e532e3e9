"use client";

import { useEffect, useState } from "react";

interface ConfettiPiece {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  rotation: number;
  rotationSpeed: number;
  color: string;
  size: number;
  opacity: number;
}

interface ConfettiEffectProps {
  isActive: boolean;
  duration?: number;
  onComplete?: () => void;
}

export default function ConfettiEffect({
  isActive,
  duration = 3000,
  onComplete
}: ConfettiEffectProps) {
  const [confetti, setConfetti] = useState<ConfettiPiece[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Brat主题颜色
  const colors = [
    '#8ACF00', // brat绿
    '#6BA000', // 深绿
    '#A8E033', // 浅绿
    '#FFFFFF', // 白色
    '#FFD700', // 金色
    '#FF69B4', // 粉色
    '#00CED1', // 青色
    '#FF6B6B', // 红色
  ];

  const createConfettiPiece = (id: number): ConfettiPiece => {
    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    return {
      id,
      x: Math.random() * screenWidth,
      y: -20,
      vx: (Math.random() - 0.5) * 6,
      vy: Math.random() * 2 + 3,
      rotation: Math.random() * 360,
      rotationSpeed: (Math.random() - 0.5) * 8,
      color: colors[Math.floor(Math.random() * colors.length)],
      size: Math.random() * 6 + 6,
      opacity: 1
    };
  };

  useEffect(() => {
    if (!isActive) {
      setIsVisible(false);
      setConfetti([]);
      return;
    }

    setIsVisible(true);

    // 创建初始彩纸爆发
    const initialConfetti = Array.from({ length: 80 }, (_, i) => createConfettiPiece(i));
    setConfetti(initialConfetti);

    // 动画循环
    let animationId: number;

    const animate = () => {
      setConfetti(prev =>
        prev
          .map(piece => ({
            ...piece,
            x: piece.x + piece.vx,
            y: piece.y + piece.vy,
            rotation: piece.rotation + piece.rotationSpeed,
            vy: piece.vy + 0.15, // 重力效果
            opacity: piece.y > window.innerHeight * 0.8 ? piece.opacity - 0.02 : piece.opacity
          }))
          .filter(piece => piece.y < window.innerHeight + 100 && piece.opacity > 0)
      );

      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    // 添加更多彩纸波次
    const wave1 = setTimeout(() => {
      setConfetti(prev => [
        ...prev,
        ...Array.from({ length: 40 }, (_, i) => createConfettiPiece(prev.length + i))
      ]);
    }, 300);

    const wave2 = setTimeout(() => {
      setConfetti(prev => [
        ...prev,
        ...Array.from({ length: 30 }, (_, i) => createConfettiPiece(prev.length + i))
      ]);
    }, 600);

    // 清理
    const cleanup = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => {
        setConfetti([]);
        onComplete?.();
      }, 1000);
    }, duration);

    return () => {
      cancelAnimationFrame(animationId);
      clearTimeout(wave1);
      clearTimeout(wave2);
      clearTimeout(cleanup);
    };
  }, [isActive, duration, onComplete]);

  if (!isVisible) return null;

  const renderConfettiPiece = (piece: ConfettiPiece) => {
    return (
      <div
        key={piece.id}
        className="absolute pointer-events-none"
        style={{
          left: piece.x,
          top: piece.y,
          width: piece.size,
          height: piece.size,
          backgroundColor: piece.color,
          transform: `rotate(${piece.rotation}deg)`,
          opacity: piece.opacity,
          borderRadius: Math.random() > 0.5 ? '50%' : '2px',
        }}
      />
    );
  };

  return (
    <div className="fixed inset-0 pointer-events-none z-40 overflow-hidden">
      {confetti.map(renderConfettiPiece)}

      {/* 成功消息 */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none">
        <div className="bg-[#8ACF00] text-black px-8 py-4 rounded-2xl shadow-2xl animate-pulse">
          <div className="text-4xl mb-2">🎉</div>
          <div className="text-xl font-black font-['Arial_Black',Arial,sans-serif]">
            Download Complete!
          </div>
          <div className="text-sm font-medium mt-1">
            Your brat image is ready!
          </div>
        </div>
      </div>
    </div>
  );
}
