/* Marquee animations */
@keyframes marquee-right {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes marquee-left {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee-right {
  animation: marquee-right linear infinite;
}

.animate-marquee-left {
  animation: marquee-left linear infinite;
}

/* Ensure smooth scrolling */
.animate-marquee-right,
.animate-marquee-left {
  will-change: transform;
  /* Enable hardware acceleration for better mobile performance */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .animate-marquee-right,
  .animate-marquee-left {
    animation-duration: var(--mobile-duration) !important;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .animate-marquee-right,
    .animate-marquee-left {
      animation-duration: 120s !important;
    }
  }
}
