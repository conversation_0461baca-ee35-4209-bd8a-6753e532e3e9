import { Section as SectionType } from "@/types/blocks/section";
import ImageMarquee from "@/components/image-marquee";
import { marqueeImages } from "@/data/marquee-images";

export default function Branding({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      {/* Image Marquee */}
      <ImageMarquee images={marqueeImages} speed={40} mobileSpeed={20} />

      <div className="container flex flex-row items-center justify-center mt-8">
        <div className="flex flex-col items-center gap-4">
          <h2 className="text-center: text-muted-foreground lg:text-left">
            {section.title}
          </h2>
          <div className="flex flex-wrap items-center justify-center gap-8 mt-4">
            {section.items?.map((item, idx) => {
              if (item.image) {
                return (
                  <img
                    key={idx}
                    src={item.image.src}
                    alt={item.image.alt || item.title}
                    className="h-12 dark:invert"
                  />
                );
              }
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
