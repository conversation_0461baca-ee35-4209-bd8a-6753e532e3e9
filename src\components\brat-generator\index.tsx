"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Download } from "lucide-react";
import DownloadCountdownModal from "@/components/ui/download-countdown-modal";
import ConfettiEffect from "@/components/ui/confetti-effect";

export default function BratGenerator() {
  // Initial state values
  const initialState = {
    text: "brat",
    backgroundColor: "#8ACE00",
    textColor: "#000000",
    fontSize: 60,
    blurEffect: 1,
    textAlignment: "center" as "left" | "center" | "right",
    textStyle: "normal" as "normal" | "mirrored"
  };

  const [text, setText] = useState(initialState.text);
  const [backgroundColor, setBackgroundColor] = useState(initialState.backgroundColor);
  const [textColor, setTextColor] = useState(initialState.textColor);
  const [fontSize, setFontSize] = useState(initialState.fontSize);
  const [blurEffect, setBlurEffect] = useState(initialState.blurEffect);
  const [textAlignment, setTextAlignment] = useState<"left" | "center" | "right">(initialState.textAlignment);
  const [textStyle, setTextStyle] = useState<"normal" | "mirrored">(initialState.textStyle);
  const [showBackgroundColorPicker, setShowBackgroundColorPicker] = useState(false);
  const [showTextColorPicker, setShowTextColorPicker] = useState(false);
  const [textLines, setTextLines] = useState<string[]>([]);
  const [isClient, setIsClient] = useState(false);

  // 下载相关状态
  const [showCountdownModal, setShowCountdownModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  // Reset function to restore initial state
  const resetToInitialState = () => {
    setText(initialState.text);
    setBackgroundColor(initialState.backgroundColor);
    setTextColor(initialState.textColor);
    setFontSize(initialState.fontSize);
    setBlurEffect(initialState.blurEffect);
    setTextAlignment(initialState.textAlignment);
    setTextStyle(initialState.textStyle);
    setShowBackgroundColorPicker(false);
    setShowTextColorPicker(false);
  };

  // Clear text function
  const clearText = () => {
    setText("");
  };

  // Quick color presets
  const quickColors = [
    "#8ACE00", // Green (default)
    "#0000FF", // Blue
    "#FF0000", // Red
    "#FFA500", // Orange
    "#FFFF00", // Yellow
    "#FF00FF", // Magenta
    "#00FFFF", // Cyan
    "#FFFFFF", // White
    "#000000", // Black
    "#800080", // Purple
    "#FFC0CB", // Pink
    "#808080"  // Gray
  ];



  // Legacy function for manual font size (when user adjusts slider)
  const calculateTextLines = (text: string, fontSize: number, maxWidth: number) => {
    if (typeof document === 'undefined') {
      return [text.toLowerCase()];
    }

    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return [text.toLowerCase()];

    const dpr = window.devicePixelRatio || 1;
    tempCanvas.width = 1000 * dpr;
    tempCanvas.height = 100 * dpr;
    tempCtx.scale(dpr, dpr);

    tempCtx.font = `${fontSize}px Arial, sans-serif`;
    tempCtx.textBaseline = 'middle';

    const lowerText = text.toLowerCase();
    const words = lowerText.split(" ");
    const lines: string[] = [];
    let currentLine = "";
    const conservativeMaxWidth = maxWidth * 0.95;

    const breakLongWord = (word: string): string[] => {
      const wordParts: string[] = [];
      let currentPart = "";

      for (let i = 0; i < word.length; i++) {
        const testPart = currentPart + word[i];
        const metrics = tempCtx.measureText(testPart);

        if (metrics.width > conservativeMaxWidth && currentPart) {
          wordParts.push(currentPart);
          currentPart = word[i];
        } else {
          currentPart = testPart;
        }
      }

      if (currentPart) {
        wordParts.push(currentPart);
      }

      return wordParts.length > 0 ? wordParts : [word];
    };

    words.forEach((word) => {
      // 检查单个单词是否太长
      const wordMetrics = tempCtx.measureText(word);

      if (wordMetrics.width > conservativeMaxWidth) {
        // 如果当前行有内容，先保存
        if (currentLine) {
          lines.push(currentLine);
          currentLine = "";
        }

        // 拆分长单词
        const wordParts = breakLongWord(word);
        wordParts.forEach((part, index) => {
          if (index === wordParts.length - 1) {
            // 最后一部分可能可以与下一个单词合并
            currentLine = part;
          } else {
            // 前面的部分直接作为独立行
            lines.push(part);
          }
        });
      } else {
        // 正常处理短单词
        const testLine = currentLine + (currentLine ? " " : "") + word;
        const metrics = tempCtx.measureText(testLine);

        if (metrics.width > conservativeMaxWidth && currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          currentLine = testLine;
        }
      }
    });

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  };



  // Set client-side flag and calculate preview lines
  useEffect(() => {
    setIsClient(true);

    const containerWidth = 400;

    // Use user-specified font size
    const padding = containerWidth * 0.04 * 2;
    const maxWidth = (containerWidth - padding) * 0.90;
    const lines = calculateTextLines(text, fontSize, maxWidth);
    setTextLines(lines);
  }, [text, fontSize]);

  // Close color pickers when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.color-picker-container')) {
        setShowBackgroundColorPicker(false);
        setShowTextColorPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Color picker component
  const ColorPicker = ({
    color,
    onChange,
    onClose
  }: {
    color: string;
    onChange: (color: string) => void;
    onClose: () => void;
  }) => {
    const [tempColor, setTempColor] = useState(color);
    const [r, setR] = useState(0);
    const [g, setG] = useState(0);
    const [b, setB] = useState(0);
    const [hue, setHue] = useState(0);
    const [saturation, setSaturation] = useState(100);
    const [lightness, setLightness] = useState(50);
    const [isDragging, setIsDragging] = useState(false);
    const [isHueDragging, setIsHueDragging] = useState(false);
    const colorAreaRef = useRef<HTMLDivElement>(null);
    const hueBarRef = useRef<HTMLDivElement>(null);

    // Convert hex to RGB
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    };

    // Convert RGB to hex
    const rgbToHex = (r: number, g: number, b: number) => {
      const toHex = (c: number) => {
        const hex = Math.round(Math.max(0, Math.min(255, c))).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      };
      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    };

    // Convert HSL to RGB
    const hslToRgb = (h: number, s: number, l: number) => {
      h /= 360;
      s /= 100;
      l /= 100;

      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      let r, g, b;
      if (s === 0) {
        r = g = b = l;
      } else {
        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
      }

      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    };

    // Convert RGB to HSL
    const rgbToHsl = (r: number, g: number, b: number) => {
      r /= 255;
      g /= 255;
      b /= 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0, s = 0, l = (max + min) / 2;

      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
      }

      return {
        h: h * 360,
        s: s * 100,
        l: l * 100
      };
    };

    // Initialize RGB and HSL values from current color
    useEffect(() => {
      const rgb = hexToRgb(color);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setR(rgb.r);
      setG(rgb.g);
      setB(rgb.b);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setLightness(hsl.l);
      setTempColor(color);
    }, [color]);

    // Update color when RGB changes
    const updateColorFromRGB = (newR: number, newG: number, newB: number) => {
      const newColor = rgbToHex(newR, newG, newB);
      const hsl = rgbToHsl(newR, newG, newB);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setLightness(hsl.l);
      setTempColor(newColor);
    };

    // Update color when HSL changes
    const updateColorFromHSL = (newH: number, newS: number, newL: number) => {
      const rgb = hslToRgb(newH, newS, newL);
      const newColor = rgbToHex(rgb.r, rgb.g, rgb.b);
      setR(rgb.r);
      setG(rgb.g);
      setB(rgb.b);
      setTempColor(newColor);
    };

    // Handle color area click/drag
    const handleColorAreaInteraction = (event: React.MouseEvent<HTMLDivElement>) => {
      const rect = colorAreaRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const newSaturation = Math.max(0, Math.min(100, (x / rect.width) * 100));
      const newLightness = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));

      setSaturation(newSaturation);
      setLightness(newLightness);
      updateColorFromHSL(hue, newSaturation, newLightness);
    };

    // Handle hue bar click/drag
    const handleHueBarInteraction = (event: React.MouseEvent<HTMLDivElement>) => {
      const rect = hueBarRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = event.clientX - rect.left;
      const newHue = Math.max(0, Math.min(360, (x / rect.width) * 360));

      setHue(newHue);
      updateColorFromHSL(newHue, saturation, lightness);
    };

    // Mouse event handlers
    useEffect(() => {
      const handleMouseMove = (event: MouseEvent) => {
        if (isDragging && colorAreaRef.current) {
          const rect = colorAreaRef.current.getBoundingClientRect();
          const x = event.clientX - rect.left;
          const y = event.clientY - rect.top;
          const newSaturation = Math.max(0, Math.min(100, (x / rect.width) * 100));
          const newLightness = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));

          setSaturation(newSaturation);
          setLightness(newLightness);
          updateColorFromHSL(hue, newSaturation, newLightness);
        }

        if (isHueDragging && hueBarRef.current) {
          const rect = hueBarRef.current.getBoundingClientRect();
          const x = event.clientX - rect.left;
          const newHue = Math.max(0, Math.min(360, (x / rect.width) * 360));

          setHue(newHue);
          updateColorFromHSL(newHue, saturation, lightness);
        }
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        setIsHueDragging(false);
      };

      if (isDragging || isHueDragging) {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
      }

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }, [isDragging, isHueDragging, hue, saturation, lightness]);

    return (
      <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-300 rounded-lg shadow-lg z-50 w-96">
        {/* Hex input and color preview */}
        <div className="flex items-center gap-3 mb-4">
          <input
            type="text"
            value={tempColor}
            onChange={(e) => {
              setTempColor(e.target.value);
              const rgb = hexToRgb(e.target.value);
              setR(rgb.r);
              setG(rgb.g);
              setB(rgb.b);
            }}
            className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm font-mono"
            placeholder="#000000"
          />
          <div
            className="w-12 h-12 rounded border border-gray-300"
            style={{ backgroundColor: tempColor }}
          />
        </div>

        {/* Color gradient area */}
        <div className="mb-4 relative">
          <div
            ref={colorAreaRef}
            className="w-full h-32 rounded cursor-crosshair"
            style={{
              background: `linear-gradient(to bottom,
                rgba(255,255,255,1) 0%,
                rgba(255,255,255,0) 50%,
                rgba(0,0,0,0) 50%,
                rgba(0,0,0,1) 100%),
                hsl(${hue}, 100%, 50%)`
            }}
            onMouseDown={(e) => {
              setIsDragging(true);
              handleColorAreaInteraction(e);
            }}
            onClick={handleColorAreaInteraction}
          />
          {/* Color circle indicator */}
          <div
            className="absolute w-4 h-4 border-2 border-white rounded-full shadow-lg pointer-events-none"
            style={{
              left: `${saturation}%`,
              top: `${100 - lightness}%`,
              transform: 'translate(-50%, -50%)',
              backgroundColor: tempColor
            }}
          />
        </div>

        {/* Hue bar */}
        <div className="mb-4 relative">
          <div
            ref={hueBarRef}
            className="w-full h-4 rounded cursor-pointer"
            style={{
              background: 'linear-gradient(to right, #ff0000 0%, #ffff00 16.66%, #00ff00 33.33%, #00ffff 50%, #0000ff 66.66%, #ff00ff 83.33%, #ff0000 100%)'
            }}
            onMouseDown={(e) => {
              setIsHueDragging(true);
              handleHueBarInteraction(e);
            }}
            onClick={handleHueBarInteraction}
          />
          {/* Hue indicator */}
          <div
            className="absolute w-2 h-6 border-2 border-white rounded shadow-lg pointer-events-none"
            style={{
              left: `${(hue / 360) * 100}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)',
              backgroundColor: `hsl(${hue}, 100%, 50%)`
            }}
          />
        </div>

        {/* RGB inputs */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="text-center">
            <input
              type="number"
              min="0"
              max="255"
              value={r}
              onChange={(e) => {
                const newR = Number(e.target.value);
                setR(newR);
                updateColorFromRGB(newR, g, b);
              }}
              className="w-full px-2 py-1 border border-gray-300 rounded text-center text-sm"
            />
            <label className="text-xs text-gray-600 mt-1 block">R</label>
          </div>
          <div className="text-center">
            <input
              type="number"
              min="0"
              max="255"
              value={g}
              onChange={(e) => {
                const newG = Number(e.target.value);
                setG(newG);
                updateColorFromRGB(r, newG, b);
              }}
              className="w-full px-2 py-1 border border-gray-300 rounded text-center text-sm"
            />
            <label className="text-xs text-gray-600 mt-1 block">G</label>
          </div>
          <div className="text-center">
            <input
              type="number"
              min="0"
              max="255"
              value={b}
              onChange={(e) => {
                const newB = Number(e.target.value);
                setB(newB);
                updateColorFromRGB(r, g, newB);
              }}
              className="w-full px-2 py-1 border border-gray-300 rounded text-center text-sm"
            />
            <label className="text-xs text-gray-600 mt-1 block">B</label>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2">
          <Button
            onClick={() => {
              onChange(tempColor);
              onClose();
            }}
            className="flex-1"
            size="sm"
          >
            Apply
          </Button>
          <Button
            onClick={onClose}
            variant="outline"
            className="flex-1"
            size="sm"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  };

  const handleDownloadClick = () => {
    // 显示倒计时弹窗
    setShowCountdownModal(true);
  };

  const performDownload = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set high-resolution canvas size for crisp output
    const resolution = 2000; // Higher resolution for better quality
    canvas.width = resolution;
    canvas.height = resolution;

    // Fill background (no blur on background)
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Set text properties to match preview
    ctx.fillStyle = textColor;

    // Scaling for high-resolution canvas (2000x2000 canvas vs ~400px preview)
    const scaleFactor = resolution / 400; // 2000 / 400 = 5

    // Calculate font size to match preview (use user font size)
    const canvasFontSize = fontSize * scaleFactor;
    ctx.font = `${canvasFontSize}px Arial, sans-serif`;
    ctx.textBaseline = "middle";

    // Apply blur effect only to text if needed
    if (blurEffect > 0) {
      const scaledBlur = blurEffect * scaleFactor;
      ctx.filter = `blur(${scaledBlur}px)`;
    }

    // Apply mirrored effect if needed
    if (textStyle === "mirrored") {
      ctx.save();
      ctx.scale(-1, 1);
      ctx.translate(-canvas.width, 0);
    }

    // Calculate consistent padding for layout (4% of canvas size)
    const padding = canvas.width * 0.04; // Standard padding

    // Use the same lines calculated in the preview (already optimized)
    const lines = textLines;

    // Draw text lines with proper spacing and alignment (match CSS lineHeight: 1.1)
    const lineHeight = canvasFontSize * 1.1;
    const totalHeight = lines.length * lineHeight;
    const startY = (canvas.height - totalHeight) / 2 + lineHeight / 2;

    lines.forEach((line, index) => {
      // Calculate X position for each line based on alignment
      let textX = canvas.width / 2; // Default center

      if (textAlignment === "left") {
        textX = padding;
      } else if (textAlignment === "right") {
        const lineWidth = ctx.measureText(line).width;
        textX = canvas.width - padding - lineWidth;
      } else {
        // Center alignment - measure text width and center it
        const lineWidth = ctx.measureText(line).width;
        textX = (canvas.width - lineWidth) / 2;
      }

      ctx.fillText(line, textX, startY + index * lineHeight);
    });

    // Restore canvas state if mirrored
    if (textStyle === "mirrored") {
      ctx.restore();
    }



    // Download the image
    const link = document.createElement("a");
    link.download = `brat-cover-${Date.now()}.png`;
    link.href = canvas.toDataURL("image/png", 1.0);
    link.click();

    // 显示撒花效果
    setShowConfetti(true);
  };

  const handleCountdownComplete = () => {
    setShowCountdownModal(false);
    performDownload();
  };

  const handleCountdownClose = () => {
    setShowCountdownModal(false);
  };

  const handleConfettiComplete = () => {
    setShowConfetti(false);
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Text Input */}
      <div className="mb-6">
        <div className="relative">
          <Input
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Enter your text"
            className="text-lg py-3 pr-12 border-2 border-gray-300 rounded-md focus:border-gray-500 focus:ring-0"
          />
          {text && (
            <button
              onClick={clearText}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear text"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Main Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Preview */}
        <div className="space-y-4">
          <div
            className="aspect-square rounded-lg relative shadow-lg overflow-hidden mx-auto"
            style={{
              backgroundColor: backgroundColor,
              width: "min(100%, 400px)", // Fixed max width for consistency
              height: "min(100vw, 400px)" // Maintain square aspect ratio
            }}
          >
            <div
              className="absolute inset-0 flex items-center"
              style={{
                justifyContent: textAlignment === "left" ? "flex-start" : textAlignment === "right" ? "flex-end" : "center",
                padding: "4%" // Standard padding
              }}
            >
            <div
              ref={previewRef}
              className="leading-tight"
              style={{
                color: textColor,
                fontFamily: "Arial, sans-serif",
                textTransform: "lowercase",
                letterSpacing: "-0.02em",
                lineHeight: "1.1", // Standard line spacing
                fontSize: `${fontSize}px`, // Use user font size
                textAlign: textAlignment,
                filter: blurEffect > 0 ? `blur(${blurEffect}px)` : "none",
                width: "100%",
                overflow: "hidden",
                wordBreak: "break-word", // 强制长单词换行
                transform: textStyle === "mirrored" ? "scaleX(-1)" : "none",
                position: "static",
                overflowWrap: "break-word", // 兼容性
                hyphens: "none" // 不使用连字符
              }}

            >
              {isClient ? (
                textLines.map((line, index) => (
                  <div key={index}>{line}</div>
                ))
              ) : (
                <div>{text.toLowerCase()}</div>
              )}
            </div>

            </div>
          </div>
        </div>

        {/* Right Side - Controls */}
        <div className="space-y-6">
          {/* Color Controls */}
          <div className="space-y-4">
            {/* Background Color */}
            <div className="space-y-2 relative color-picker-container">
              <label className="text-sm font-medium text-gray-700">
                Background Color
              </label>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-24 px-3 py-2 border border-gray-300 rounded text-sm font-mono"
                    placeholder="#8ACE00"
                  />
                  <button
                    onClick={() => setShowBackgroundColorPicker(!showBackgroundColorPicker)}
                    className="w-8 h-8 rounded border-2 border-red-500 hover:border-red-600 transition-colors cursor-pointer"
                    style={{ backgroundColor: backgroundColor }}
                    title="Click to open color picker"
                  />
                </div>

                {/* Quick color presets for background - responsive layout */}
                <div className="flex gap-1 flex-wrap">
                  {quickColors.map((color, index) => (
                    <button
                      key={index}
                      onClick={() => setBackgroundColor(color)}
                      className={`w-7 h-7 sm:w-6 sm:h-6 rounded-full border-2 hover:scale-110 transition-transform ${
                        backgroundColor === color ? 'border-gray-800' : 'border-gray-300'
                      } ${color === '#FFFFFF' ? 'border-gray-400' : ''}`}
                      style={{ backgroundColor: color }}
                      title={`Set background to ${color}`}
                    />
                  ))}
                </div>
              </div>

              {showBackgroundColorPicker && (
                <ColorPicker
                  color={backgroundColor}
                  onChange={(color) => setBackgroundColor(color)}
                  onClose={() => setShowBackgroundColorPicker(false)}
                />
              )}
            </div>

            {/* Text Color */}
            <div className="space-y-2 relative color-picker-container">
              <label className="text-sm font-medium text-gray-700">
                Text Color
              </label>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="w-24 px-3 py-2 border border-gray-300 rounded text-sm font-mono"
                    placeholder="#000000"
                  />
                  <button
                    onClick={() => setShowTextColorPicker(!showTextColorPicker)}
                    className="w-8 h-8 rounded border-2 border-red-500 hover:border-red-600 transition-colors cursor-pointer"
                    style={{ backgroundColor: textColor }}
                    title="Click to open color picker"
                  />
                </div>

                {/* Quick color presets for text - responsive layout */}
                <div className="flex gap-1 flex-wrap">
                  {quickColors.map((color, index) => (
                    <button
                      key={index}
                      onClick={() => setTextColor(color)}
                      className={`w-7 h-7 sm:w-6 sm:h-6 rounded-full border-2 hover:scale-110 transition-transform ${
                        textColor === color ? 'border-gray-800' : 'border-gray-300'
                      } ${color === '#FFFFFF' ? 'border-gray-400' : ''}`}
                      style={{ backgroundColor: color }}
                      title={`Set text color to ${color}`}
                    />
                  ))}
                </div>
              </div>

              {showTextColorPicker && (
                <ColorPicker
                  color={textColor}
                  onChange={(color) => setTextColor(color)}
                  onClose={() => setShowTextColorPicker(false)}
                />
              )}
            </div>
          </div>



          {/* Text Style Control */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Text Style</label>
            <div className="flex gap-2">
              {(["normal", "mirrored"] as const).map((style) => (
                <Button
                  key={style}
                  variant={textStyle === style ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTextStyle(style)}
                  className="flex-1 capitalize"
                >
                  {style === "normal" ? "Normal" : "Mirrored"}
                </Button>
              ))}
            </div>
          </div>

          {/* Font Size Control */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="20"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((fontSize - 20) / (120 - 20)) * 100}%, #e5e7eb ${((fontSize - 20) / (120 - 20)) * 100}%, #e5e7eb 100%)`
              }}
            />
          </div>

          {/* Blur Effect Control */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Blur Effect: {blurEffect}px
            </label>
            <input
              type="range"
              min="0"
              max="10"
              value={blurEffect}
              onChange={(e) => setBlurEffect(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(blurEffect / 10) * 100}%, #e5e7eb ${(blurEffect / 10) * 100}%, #e5e7eb 100%)`
              }}
            />
          </div>

          {/* Text Alignment */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Text Alignment</label>
            <div className="flex gap-2">
              {(["left", "center", "right"] as const).map((align) => (
                <Button
                  key={align}
                  variant={textAlignment === align ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTextAlignment(align)}
                  className="flex-1 capitalize"
                >
                  {align}
                </Button>
              ))}
            </div>
          </div>

          {/* Download Button */}
          <Button
            onClick={handleDownloadClick}
            className="w-full py-3 text-base font-semibold bg-gray-600 text-white hover:bg-gray-700 rounded-md transition-colors"
            size="lg"
          >
            <Download className="mr-2" size={18} />
            Download Image
          </Button>

          {/* Reset Button */}
          <Button
            onClick={resetToInitialState}
            className="w-full py-3 text-base font-semibold bg-red-500 text-white hover:bg-red-600 rounded-md transition-colors"
            size="lg"
          >
            <svg className="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Reset to Default
          </Button>
        </div>
      </div>

      {/* Hidden canvas for image generation */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 倒计时弹窗 */}
      <DownloadCountdownModal
        isOpen={showCountdownModal}
        onComplete={handleCountdownComplete}
        onClose={handleCountdownClose}
        countdown={5}
      />

      {/* 撒花效果 */}
      <ConfettiEffect
        isActive={showConfetti}
        duration={3000}
        onComplete={handleConfettiComplete}
      />
    </div>
  );
}
