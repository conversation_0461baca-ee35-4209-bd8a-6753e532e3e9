"use client";

import React from 'react';
import './marquee.css';

interface ImageMarqueeProps {
  images: string[];
  speed?: number;
  mobileSpeed?: number;
}

export default function ImageMarquee({ images, speed = 50, mobileSpeed = 25 }: ImageMarqueeProps) {
  // Split images into two rows
  const firstRowImages = images.slice(0, Math.ceil(images.length / 2));
  const secondRowImages = images.slice(Math.ceil(images.length / 2));

  return (
    <div className="w-full overflow-hidden bg-gray-50 py-8">
      {/* First row - moving right */}
      <div className="relative mb-4">
        <div
          className="flex gap-4 animate-marquee-right"
          style={{
            animationDuration: `${speed}s`,
            animationTimingFunction: 'linear',
            animationIterationCount: 'infinite',
            // Use faster speed on mobile
            '--mobile-duration': `${mobileSpeed}s`
          } as React.CSSProperties}
        >
          {/* Duplicate images for seamless loop */}
          {[...firstRowImages, ...firstRowImages].map((imageUrl, index) => (
            <div
              key={index}
              className="flex-shrink-0 w-48 h-48 md:w-64 md:h-64 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
            >
              <img
                src={imageUrl}
                alt={`Brat meme ${index + 1}`}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Second row - moving left */}
      <div className="relative">
        <div
          className="flex gap-4 animate-marquee-left"
          style={{
            animationDuration: `${speed}s`,
            animationTimingFunction: 'linear',
            animationIterationCount: 'infinite',
            // Use faster speed on mobile
            '--mobile-duration': `${mobileSpeed}s`
          } as React.CSSProperties}
        >
          {/* Duplicate images for seamless loop */}
          {[...secondRowImages, ...secondRowImages].map((imageUrl, index) => (
            <div
              key={index}
              className="flex-shrink-0 w-48 h-48 md:w-64 md:h-64 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
            >
              <img
                src={imageUrl}
                alt={`Brat meme ${index + 1}`}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
