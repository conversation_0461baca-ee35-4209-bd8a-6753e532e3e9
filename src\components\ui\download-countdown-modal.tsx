"use client";

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

interface DownloadCountdownModalProps {
  isOpen: boolean;
  onComplete: () => void;
  onClose: () => void;
  countdown?: number;
}

export default function DownloadCountdownModal({
  isOpen,
  onComplete,
  onClose,
  countdown = 5
}: DownloadCountdownModalProps) {
  const [timeLeft, setTimeLeft] = useState(countdown);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTimeLeft(countdown);
      setIsVisible(true);
      
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setTimeout(() => {
              setIsVisible(false);
              onComplete();
            }, 500); // 延迟半秒让用户看到0
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    } else {
      setIsVisible(false);
    }
  }, [isOpen, countdown, onComplete]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <div 
        className={cn(
          "relative bg-[#8ACF00] rounded-2xl p-8 mx-4 max-w-md w-full text-center shadow-2xl transform transition-all duration-500",
          isVisible ? "scale-100 opacity-100" : "scale-95 opacity-0"
        )}
      >
        {/* 标题 */}
        <h2 className="text-white text-2xl font-black mb-8 font-['Arial_Black',Arial,sans-serif] tracking-tight">
          Downloading...
        </h2>
        
        {/* 倒计时数字 */}
        <div className="relative mb-6">
          <div 
            className={cn(
              "text-8xl font-black text-black font-['Arial_Black',Arial,sans-serif] transition-all duration-300",
              timeLeft === 0 ? "scale-110 text-white" : "scale-100"
            )}
          >
            {timeLeft}
          </div>
          
          {/* 圆形进度条 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
              {/* 背景圆 */}
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="rgba(0,0,0,0.1)"
                strokeWidth="4"
                fill="none"
              />
              {/* 进度圆 */}
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="rgba(0,0,0,0.3)"
                strokeWidth="4"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 45}`}
                strokeDashoffset={`${2 * Math.PI * 45 * (timeLeft / countdown)}`}
                className="transition-all duration-1000 ease-linear"
              />
            </svg>
          </div>
        </div>
        
        {/* 提示文字 */}
        <p className="text-black text-sm font-medium opacity-80">
          {timeLeft > 0 ? "Preparing your brat image..." : "Ready! 🎉"}
        </p>
        
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-black hover:text-white transition-colors duration-200"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
}
