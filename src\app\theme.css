:root {
  /* Brat Generator Theme - Inspired by Charli XCX Brat Album */

  /* Core Brat Colors */
  --brat-green: #8ACF00; /* Signature brat lime green #8ACF00 */
  --brat-green-dark: #6BA000; /* Darker brat green */
  --brat-green-light: #A8E033; /* Lighter brat green */
  --brat-black: oklch(0.15 0 0); /* Deep black for text */
  --brat-white: oklch(0.98 0 0); /* Clean white */
  --brat-gray: oklch(0.85 0 0); /* Light gray */
  --brat-gray-dark: oklch(0.45 0 0); /* Dark gray */

  /* Main Theme Variables */
  --background: var(--brat-white);
  --foreground: var(--brat-black);
  --card: var(--brat-white);
  --card-foreground: var(--brat-black);
  --popover: var(--brat-white);
  --popover-foreground: var(--brat-black);
  --primary: var(--brat-green);
  --primary-foreground: var(--brat-black);
  --secondary: var(--brat-gray);
  --secondary-foreground: var(--brat-black);
  --muted: var(--brat-gray);
  --muted-foreground: var(--brat-gray-dark);
  --accent: var(--brat-green-light);
  --accent-foreground: var(--brat-black);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: var(--brat-white);
  --border: oklch(0.9 0 0);
  --input: var(--brat-white);
  --ring: var(--brat-green);
  --chart-1: var(--brat-green);
  --chart-2: var(--brat-green-dark);
  --chart-3: var(--brat-green-light);
  --chart-4: var(--brat-gray-dark);
  --chart-5: var(--brat-black);
  --sidebar: var(--brat-white);
  --sidebar-foreground: var(--brat-black);
  --sidebar-primary: var(--brat-green);
  --sidebar-primary-foreground: var(--brat-black);
  --sidebar-accent: var(--brat-green-light);
  --sidebar-accent-foreground: var(--brat-black);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: var(--brat-green);
  --font-sans: "Arial Black", Arial, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

.dark {
  /* Dark Brat Theme - Inverted brat aesthetic */

  /* Dark Brat Colors */
  --brat-green-dark-mode: #6BA000; /* Slightly muted brat green for dark mode */
  --brat-green-bright: #8ACF00; /* Bright brat green for accents */
  --brat-dark-bg: oklch(0.08 0 0); /* Very dark background */
  --brat-dark-card: oklch(0.12 0 0); /* Dark card background */
  --brat-dark-gray: oklch(0.25 0 0); /* Dark gray */
  --brat-light-text: oklch(0.95 0 0); /* Light text */

  --background: var(--brat-dark-bg);
  --foreground: var(--brat-light-text);
  --card: var(--brat-dark-card);
  --card-foreground: var(--brat-light-text);
  --popover: var(--brat-dark-bg);
  --popover-foreground: var(--brat-light-text);
  --primary: var(--brat-green-dark-mode);
  --primary-foreground: var(--brat-black);
  --secondary: var(--brat-dark-gray);
  --secondary-foreground: var(--brat-light-text);
  --muted: var(--brat-dark-gray);
  --muted-foreground: oklch(0.6 0 0);
  --accent: var(--brat-dark-card);
  --accent-foreground: var(--brat-green-bright);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: var(--brat-white);
  --border: oklch(0.2 0 0);
  --input: var(--brat-dark-card);
  --ring: var(--brat-green-dark-mode);
  --chart-1: var(--brat-green-dark-mode);
  --chart-2: var(--brat-green-bright);
  --chart-3: oklch(0.8 0.2 130);
  --chart-4: var(--brat-dark-gray);
  --chart-5: var(--brat-light-text);
  --sidebar: var(--brat-dark-card);
  --sidebar-foreground: var(--brat-light-text);
  --sidebar-primary: var(--brat-green-dark-mode);
  --sidebar-primary-foreground: var(--brat-black);
  --sidebar-accent: var(--brat-dark-gray);
  --sidebar-accent-foreground: var(--brat-green-bright);
  --sidebar-border: oklch(0.2 0 0);
  --sidebar-ring: var(--brat-green-dark-mode);
  --font-sans: "Arial Black", Arial, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Custom Brat Generator Styles */
.brat-button {
  background: var(--brat-green);
  color: var(--brat-black);
  font-family: "Arial Black", Arial, sans-serif;
  font-weight: 900;
  text-transform: lowercase;
  border: 2px solid var(--brat-black);
  border-radius: 0;
  padding: 12px 24px;
  font-size: 16px;
  letter-spacing: -0.5px;
  transition: all 0.2s ease;
}

.brat-button:hover {
  background: var(--brat-green-dark);
  transform: translateY(-2px);
  box-shadow: 4px 4px 0px var(--brat-black);
}

.brat-title {
  font-family: "Arial Black", Arial, sans-serif;
  font-weight: 900;
  text-transform: lowercase;
  letter-spacing: -1px;
  color: var(--brat-black);
}

.brat-text {
  font-family: "Arial", sans-serif;
  font-weight: 400;
  color: var(--brat-black);
  line-height: 1.4;
}

.brat-card {
  background: var(--brat-white);
  border: 2px solid var(--brat-black);
  border-radius: 0;
  box-shadow: 6px 6px 0px var(--brat-green);
}

.brat-input {
  background: var(--brat-white);
  border: 2px solid var(--brat-black);
  border-radius: 0;
  font-family: "Arial", sans-serif;
  font-weight: 600;
  color: var(--brat-black);
}

.brat-input:focus {
  outline: none;
  border-color: var(--brat-green);
  box-shadow: 0 0 0 3px var(--brat-green-light);
}

.brat-generator-preview {
  background: var(--brat-green);
  color: var(--brat-black);
  font-family: "Arial Black", Arial, sans-serif;
  font-weight: 900;
  text-transform: lowercase;
  letter-spacing: -1px;
  padding: 40px;
  text-align: center;
  border: 3px solid var(--brat-black);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(24px, 5vw, 48px);
}

.dark .brat-generator-preview {
  background: var(--brat-green-dark-mode);
}

.brat-nav {
  background: var(--brat-white);
  border-bottom: 3px solid var(--brat-black);
}

.dark .brat-nav {
  background: var(--brat-dark-bg);
  border-bottom: 3px solid var(--brat-green-dark-mode);
}

.brat-footer {
  background: var(--brat-black);
  color: var(--brat-white);
  border-top: 3px solid var(--brat-green);
}

/* Brat-style animations */
@keyframes brat-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.brat-animate {
  animation: brat-bounce 2s infinite;
}

/* Responsive brat text sizing */
@media (max-width: 768px) {
  .brat-generator-preview {
    font-size: clamp(18px, 4vw, 32px);
    padding: 20px;
  }
}
