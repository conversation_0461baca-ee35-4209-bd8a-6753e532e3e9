# OG Image Guide for Brat Generator

## 📸 Required Images

### 1. **Main OG Image** (`/public/og-image.png`)
- **尺寸**: 1200x630px (Facebook/Twitter推荐)
- **格式**: PNG或JPG
- **文件大小**: < 8MB (建议 < 1MB)
- **设计要求**:
  - 使用brat绿色背景 (#8ACF00)
  - 包含"Brat Generator"标题
  - 使用Arial Black字体
  - 简洁明了的设计
  - 确保在小尺寸下也清晰可读

### 2. **Favicon** (`/public/favicon.ico`)
- **尺寸**: 32x32px, 16x16px (多尺寸ICO文件)
- **设计**: 简化的brat logo或"B"字母

### 3. **Apple Touch Icon** (`/public/apple-touch-icon.png`)
- **尺寸**: 180x180px
- **格式**: PNG
- **设计**: 适合iOS设备的图标

### 4. **Manifest Icon** (用于PWA)
- **尺寸**: 192x192px, 512x512px
- **格式**: PNG
- **用途**: Android设备添加到主屏幕

## 🎨 设计建议

### **OG Image设计模板**:
```
┌─────────────────────────────────────┐
│  #8ACF00 背景                        │
│                                     │
│         BRAT GENERATOR              │
│                                     │
│    Free Charli XCX Style           │
│      Meme Generator                 │
│                                     │
│    [示例brat图片预览]                │
│                                     │
│         bratgenerator.casa          │
└─────────────────────────────────────┘
```

### **颜色方案**:
- **主色**: #8ACF00 (brat绿)
- **文字**: #000000 (黑色)
- **辅助色**: #FFFFFF (白色)

### **字体**:
- **主标题**: Arial Black, 粗体
- **副标题**: Arial, 常规
- **网址**: Arial, 小号

## 📱 各平台显示效果

### **Facebook**:
- 显示1200x630px图片
- 标题最多65个字符
- 描述最多155个字符

### **Twitter**:
- 显示1200x630px图片 (Large Image Card)
- 标题最多70个字符
- 描述最多200个字符

### **LinkedIn**:
- 显示1200x630px图片
- 标题最多150个字符
- 描述最多300个字符

### **WhatsApp**:
- 显示缩略图
- 显示标题和描述

### **Pinterest**:
- 推荐2:3比例 (1000x1500px)
- 垂直图片效果更好

## 🔧 技术要求

### **文件位置**:
```
public/
├── og-image.png          (1200x630px)
├── favicon.ico           (32x32px)
├── apple-touch-icon.png  (180x180px)
└── manifest.json         (PWA配置)
```

### **测试工具**:
- [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)
- [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

## ✅ 检查清单

- [ ] OG图片已创建并放置在 `/public/og-image.png`
- [ ] 图片尺寸正确 (1200x630px)
- [ ] 文件大小 < 1MB
- [ ] 使用brat品牌颜色
- [ ] 文字清晰可读
- [ ] 在各平台测试分享效果
- [ ] Favicon已设置
- [ ] Apple Touch Icon已设置

## 🎯 优化建议

1. **A/B测试不同设计**
2. **监控分享点击率**
3. **定期更新图片内容**
4. **确保移动端显示效果**
5. **考虑季节性或活动主题**
