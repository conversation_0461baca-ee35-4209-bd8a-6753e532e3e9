import "@/app/globals.css";

import { getLocale, setRequestLocale } from "next-intl/server";
import { locales } from "@/i18n/locale";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  setRequestLocale(locale);

  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const googleAdsenseCode = process.env.NEXT_PUBLIC_GOOGLE_ADCODE || "";

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        {googleAdsenseCode && (
          <meta name="google-adsense-account" content={googleAdsenseCode} />
        )}



        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={webUrl || "https://bratgenerator.casa"} />
        <meta property="og:title" content="Free Brat Generator - Create Charli XCX Brat Album Cover Style Images" />
        <meta property="og:description" content="Create stunning brat meme images with our free online brat generator tool. Generate custom brat album cover style graphics in seconds!" />
        <meta property="og:image" content={`${webUrl || "https://bratgenerator.casa"}/og-image.png`} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content="Brat Generator - Free Brat Meme Creator" />
        <meta property="og:site_name" content="Brat Generator" />
        <meta property="og:locale" content={locale} />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:url" content={webUrl || "https://bratgenerator.casa"} />
        <meta name="twitter:title" content="Free Brat Generator - Create Charli XCX Brat Album Cover Style Images" />
        <meta name="twitter:description" content="Create stunning brat meme images with our free online brat generator tool. Generate custom brat album cover style graphics in seconds!" />
        <meta name="twitter:image" content={`${webUrl || "https://bratgenerator.casa"}/og-image.png`} />
        <meta name="twitter:image:alt" content="Brat Generator - Free Brat Meme Creator" />
        <meta name="twitter:creator" content="@bratgenerator" />
        <meta name="twitter:site" content="@bratgenerator" />

        {/* Pinterest */}
        <meta name="pinterest-rich-pin" content="true" />
        <meta property="article:author" content="Brat Generator" />

        {/* Additional Meta Tags */}
        <meta name="theme-color" content="#8ACF00" />
        <meta name="msapplication-TileColor" content="#8ACF00" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Brat Generator" />

        {/* Favicon - SVG with fallback */}
        <link rel="icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {locales &&
          locales.map((loc) => (
            <link
              key={loc}
              rel="alternate"
              hrefLang={loc}
              href={`${webUrl}${loc === "en" ? "" : `/${loc}`}/`}
            />
          ))}
        <link rel="alternate" hrefLang="x-default" href={webUrl} />
      </head>
      <body suppressHydrationWarning>{children}</body>
    </html>
  );
}
