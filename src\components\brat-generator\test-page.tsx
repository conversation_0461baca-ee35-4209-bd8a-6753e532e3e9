"use client";

import BratGenerator from "./index";

/**
 * Test page for the Brat Generator component
 * This can be used to test the component in isolation
 */
export default function BratGeneratorTestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Brat Generator Test Page
          </h1>
          <p className="text-gray-600">
            Test the Brat Generator component functionality
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <BratGenerator />
        </div>
        
        <div className="mt-12 text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test Instructions
          </h2>
          <div className="text-left max-w-2xl mx-auto space-y-2 text-gray-600">
            <p>1. ✅ Enter custom text in the input field</p>
            <p>2. ✅ Select different colors from the color palette</p>
            <p>3. ✅ Verify the preview updates in real-time</p>
            <p>4. ✅ Test the download functionality</p>
            <p>5. ✅ Test responsive behavior on different screen sizes</p>
            <p>6. ✅ Test the clear text button (X icon)</p>
          </div>
        </div>
      </div>
    </div>
  );
}
