"use client";

import moment from "moment";
import { useEffect, useState } from "react";

interface TimeDisplayProps {
  value: string | number | Date;
  format?: string;
  relative?: boolean;
  className?: string;
}

/**
 * TimeDisplay component that handles time formatting without hydration issues
 */
export default function TimeDisplay({ 
  value, 
  format = "YYYY-MM-DD HH:mm:ss", 
  relative = false,
  className 
}: TimeDisplayProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const staticFormat = moment(value).format(format);
  
  if (!mounted) {
    // During SSR, always show the static format to avoid hydration mismatch
    return <span className={className}>{staticFormat}</span>;
  }

  // After hydration, show the requested format
  const displayValue = relative ? moment(value).fromNow() : staticFormat;

  return <span className={className}>{displayValue}</span>;
}
